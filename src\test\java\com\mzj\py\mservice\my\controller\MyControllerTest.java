package com.mzj.py.mservice.my.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StatusCode;
import com.mzj.py.mservice.my.entity.BaseAbout;
import com.mzj.py.mservice.my.service.MyService;
import com.mzj.py.mservice.my.vo.BaseServiceMessageVo;
import com.mzj.py.mservice.wxuser.entity.WxUser;
import com.mzj.py.mservice.wxuser.vo.UserVo;
import com.mzj.py.mservice.websocket.server.WebSocketNettyServer;
import com.mzj.py.mservice.websocket.config.NettyConfig;
import com.mzj.py.filter.TokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * MyController单元测试类
 * 测试MyController中的所有REST端点
 *
 * @author: Test Author
 * @date: 2024/12/12
 * @version: 1.0
 */
@WebMvcTest(MyController.class)
@AutoConfigureMockMvc(addFilters = false)
@DisplayName("MyController单元测试")
class MyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MyService myService;

    @Autowired
    private ObjectMapper objectMapper;

    private String validAccessToken;
    private String invalidAccessToken;
    private WxUser mockWxUser;
    private BaseAbout mockBaseAbout;

    @MockBean
    private WebSocketNettyServer webSocketNettyServer;

    @MockBean
    private NettyConfig nettyConfig;

    @MockBean
    private TokenUtil tokenUtil;

    @BeforeEach
    void setUp() {
        validAccessToken = "valid_token_123";
        invalidAccessToken = "invalid_token_456";

        // 设置模拟的WxUser
        mockWxUser = new WxUser();
        mockWxUser.setId(1L);
        mockWxUser.setOpenid("test_openid");
        mockWxUser.setNickname("测试用户");
        mockWxUser.setAvatar("test_avatar");
        mockWxUser.setGender(1);
        mockWxUser.setPhone("13800138000");
        mockWxUser.setArea("北京");

        // 设置模拟的BaseAbout
        mockBaseAbout = new BaseAbout();
        mockBaseAbout.setId(1L);
        mockBaseAbout.setContent("关于我们的内容");

        // Mock TokenUtil behavior
        when(tokenUtil.externalTokenCheck(validAccessToken)).thenReturn(true);
        when(tokenUtil.externalTokenCheck(invalidAccessToken)).thenReturn(false);
        when(tokenUtil.managerCheck(anyString())).thenReturn(false);
    }

    @Test
    @DisplayName("测试GET /my/info - 成功获取用户信息")
    void testMyInfo_Success() throws Exception {
        // Given
        ResultBean<WxUser> successResult = ResultBean.successfulResult(mockWxUser);
        when(myService.myInfo(validAccessToken)).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/my/info")
                        .header("accessToken", validAccessToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData.id").value(mockWxUser.getId()))
                .andExpect(jsonPath("$.resultData.openid").value(mockWxUser.getOpenid()))
                .andExpect(jsonPath("$.resultData.nickname").value(mockWxUser.getNickname()));
    }

    @Test
    @DisplayName("测试GET /my/info - Token失效")
    void testMyInfo_TokenInvalid() throws Exception {
        // Given
        ResultBean<WxUser> failedResult = ResultBean.failedResultWithMsg("token失效，请重新登录");
        when(myService.myInfo(invalidAccessToken)).thenReturn(failedResult);

        // When & Then
        mockMvc.perform(get("/my/info")
                        .header("accessToken", invalidAccessToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.ERROR_CODE_10001.getErrorCode()))
                .andExpect(jsonPath("$.msg").value("token失效，请重新登录"))
                .andExpect(jsonPath("$.resultData").isEmpty());
    }

    @Test
    @DisplayName("测试GET /my/info - 缺少accessToken请求头")
    void testMyInfo_MissingAccessToken() throws Exception {
        // When & Then
        mockMvc.perform(get("/my/info"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试GET /my/about - 成功获取关于我们信息")
    void testAboutUs_Success() throws Exception {
        // Given
        ResultBean<BaseAbout> successResult = ResultBean.successfulResult(mockBaseAbout);
        when(myService.aboutUs()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/my/about"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData.id").value(mockBaseAbout.getId()))
                .andExpect(jsonPath("$.resultData.content").value(mockBaseAbout.getContent()));
    }

    @Test
    @DisplayName("测试GET /my/about - 无数据")
    void testAboutUs_NoData() throws Exception {
        // Given
        ResultBean<BaseAbout> successResult = ResultBean.successfulResult(null);
        when(myService.aboutUs()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/my/about"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").isEmpty());
    }

    @Test
    @DisplayName("测试GET /my/customerService - 成功获取客服信息")
    void testCustomerService_Success() throws Exception {
        // Given
        ResultBean<String> successResult = ResultBean.successfulResult("客服信息");
        when(myService.customerService()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/my/customerService"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").value("客服信息"));
    }

    @Test
    @DisplayName("测试POST /my/customerMessage - 成功提交客服留言")
    void testCustomerMessage_Success() throws Exception {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("这是一条测试留言");

        ResultBean<Boolean> successResult = ResultBean.successfulResult(true);
        when(myService.customerMessage(eq(validAccessToken), any(BaseServiceMessageVo.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(post("/my/customerMessage")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(messageVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").value(true));
    }

    @Test
    @DisplayName("测试POST /my/customerMessage - Token失效")
    void testCustomerMessage_TokenInvalid() throws Exception {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("这是一条测试留言");

        ResultBean<Boolean> failedResult = ResultBean.failedResultWithMsg("token失效，请重新登录");
        when(myService.customerMessage(eq(invalidAccessToken), any(BaseServiceMessageVo.class)))
                .thenReturn(failedResult);

        // When & Then
        mockMvc.perform(post("/my/customerMessage")
                        .header("accessToken", invalidAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(messageVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.ERROR_CODE_10001.getErrorCode()))
                .andExpect(jsonPath("$.msg").value("token失效，请重新登录"));
    }

    @Test
    @DisplayName("测试POST /my/customerMessage - 留言内容为空")
    void testCustomerMessage_EmptyContent() throws Exception {
        // Given
        BaseServiceMessageVo messageVo = new BaseServiceMessageVo();
        messageVo.setContent("");

        ResultBean<String> failedResult = ResultBean.failedResultWithMsg("留言内容不能为空");
        when(myService.customerMessage(eq(validAccessToken), any(BaseServiceMessageVo.class)))
                .thenReturn(failedResult);

        // When & Then
        mockMvc.perform(post("/my/customerMessage")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(messageVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.ERROR_CODE_10001.getErrorCode()))
                .andExpect(jsonPath("$.msg").value("留言内容不能为空"));
    }

    @Test
    @DisplayName("测试POST /my/customerMessage - 缺少请求体")
    void testCustomerMessage_MissingRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/my/customerMessage")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 成功更新头像")
    void testUpdateAvatarOrNickName_UpdateAvatar_Success() throws Exception {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");

        ResultBean<String> successResult = ResultBean.successfulResult("修改头像成功");
        when(myService.updateAvatarOrNickName(eq(validAccessToken), any(UserVo.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").value("修改头像成功"));
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 成功更新昵称")
    void testUpdateAvatarOrNickName_UpdateNickname_Success() throws Exception {
        // Given
        UserVo userVo = new UserVo();
        userVo.setNickName("新昵称");

        ResultBean<String> successResult = ResultBean.successfulResult("修改昵称成功");
        when(myService.updateAvatarOrNickName(eq(validAccessToken), any(UserVo.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").value("修改昵称成功"));
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - Token失效")
    void testUpdateAvatarOrNickName_TokenInvalid() throws Exception {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");

        ResultBean<String> failedResult = ResultBean.failedResultWithMsg("token失效，请重新登录");
        when(myService.updateAvatarOrNickName(eq(invalidAccessToken), any(UserVo.class)))
                .thenReturn(failedResult);

        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", invalidAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.ERROR_CODE_10001.getErrorCode()))
                .andExpect(jsonPath("$.msg").value("token失效，请重新登录"));
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 头像和昵称都为空")
    void testUpdateAvatarOrNickName_BothEmpty() throws Exception {
        // Given
        UserVo userVo = new UserVo();
        // 头像和昵称都不设置，保持为null

        ResultBean<Boolean> successResult = ResultBean.successfulResult(true);
        when(myService.updateAvatarOrNickName(eq(validAccessToken), any(UserVo.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(StatusCode.SUCCESS_CODE_10000.getErrorCode()))
                .andExpect(jsonPath("$.msg").value(StatusCode.SUCCESS_CODE_10000.getErrorMsg()))
                .andExpect(jsonPath("$.resultData").value(true));
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 缺少accessToken请求头")
    void testUpdateAvatarOrNickName_MissingAccessToken() throws Exception {
        // Given
        UserVo userVo = new UserVo();
        userVo.setHeadPicture("new_avatar_url");

        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(userVo)))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 缺少请求体")
    void testUpdateAvatarOrNickName_MissingRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试PUT /my/avatarOrNickname - 无效的JSON格式")
    void testUpdateAvatarOrNickName_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(put("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试所有端点的HTTP方法不匹配")
    void testWrongHttpMethods() throws Exception {
        // 测试GET端点使用POST方法
        mockMvc.perform(post("/my/info")
                        .header("accessToken", validAccessToken))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());

        // 测试POST端点使用GET方法
        mockMvc.perform(get("/my/customerMessage")
                        .header("accessToken", validAccessToken))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());

        // 测试PUT端点使用GET方法
        mockMvc.perform(get("/my/avatarOrNickname")
                        .header("accessToken", validAccessToken))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @DisplayName("测试不存在的端点")
    void testNonExistentEndpoint() throws Exception {
        mockMvc.perform(get("/my/nonexistent")
                        .header("accessToken", validAccessToken))
                .andDo(print())
                .andExpect(status().isNotFound());
    }
}
