package com.mzj.py.mservice.home.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.config.exception.GlobalExceptionHandler;
import com.mzj.py.mservice.compound.entity.SpeechSynthesizerDto;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.DeviceVoiceAddParam;
import com.mzj.py.mservice.home.controller.request.RecordUploadReq;
import com.mzj.py.mservice.home.entity.AiClass;
import com.mzj.py.mservice.home.entity.AiStyle;
import com.mzj.py.mservice.home.entity.DubTemplateType;
import com.mzj.py.mservice.home.service.AnchorService;
import com.mzj.py.mservice.home.vo.AnchorVo;
import com.mzj.py.mservice.home.vo.MergeVo;
import com.mzj.py.mservice.home.vo.VoiceWorkVo;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * AnchorController单元测试类
 * 测试AnchorController中所有公共方法的业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AnchorController单元测试")
class AnchorControllerTest {

        @Mock
        private AnchorService anchorService;

        @Mock
        private OSSService ossService;

        @Mock
        private DeviceService deviceService;

        @Mock
        private RedisService redisService;

        @InjectMocks
        private AnchorController anchorController;

        private MockMvc mockMvc;
        private ObjectMapper objectMapper;
        private TokenRedisVo mockTokenRedisVo;

        @BeforeEach
        void setUp() {
                // 使用ReflectionTestUtils注入RedisService到父类ApiBaseController中
                ReflectionTestUtils.setField(anchorController, "redisService", redisService);

                mockMvc = MockMvcBuilders.standaloneSetup(anchorController)
                                .setControllerAdvice(new GlobalExceptionHandler())
                                .build();
                objectMapper = new ObjectMapper();

                // 初始化测试数据
                mockTokenRedisVo = new TokenRedisVo();
                mockTokenRedisVo.setId(1L);
                mockTokenRedisVo.setOpenid("test-openid");
        }

        @Test
        @DisplayName("测试获取主播列表 - 成功场景")
        void testList_Success() throws Exception {
                // Given
                List<AnchorVo> mockAnchorList = Arrays.asList(
                                createMockAnchorVo(1L, "促销男声"),
                                createMockAnchorVo(2L, "知米"));
                ResultBean<List<AnchorVo>> mockResult = ResultBean.successfulResult(mockAnchorList);
                when(anchorService.list(null)).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/list"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2))
                                .andExpect(jsonPath("$.resultData[0].name").value("促销男声"));

                verify(anchorService).list(null);
        }

        @Test
        @DisplayName("测试获取主播列表 - 带ID参数")
        void testList_WithId() throws Exception {
                // Given
                Long id = 1L;
                List<AnchorVo> mockAnchorList = Arrays.asList(createMockAnchorVo(1L, "促销男声"));
                ResultBean<List<AnchorVo>> mockResult = ResultBean.successfulResult(mockAnchorList);
                when(anchorService.list(id)).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/list").param("id", "1"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(1));

                verify(anchorService).list(id);
        }

        @Test
        @DisplayName("测试获取长音频主播列表 - 成功场景")
        void testLongList_Success() throws Exception {
                // Given
                List<AnchorVo> mockLongAnchorList = Arrays.asList(
                                createMockAnchorVo(1L, "智逍遥"),
                                createMockAnchorVo(2L, "智瑜"));
                ResultBean<List<AnchorVo>> mockResult = ResultBean.successfulResult(mockLongAnchorList);
                when(anchorService.longList(null)).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/longList"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2))
                                .andExpect(jsonPath("$.resultData[0].name").value("智逍遥"));

                verify(anchorService).longList(null);
        }

        @Test
        @DisplayName("测试获取主播分类 - 成功场景")
        void testTypeList_Success() throws Exception {
                // Given
                List<Map<String, String>> mockTypeList = Arrays.asList(
                                createMockTypeMap("广告配音"),
                                createMockTypeMap("新闻播报"));
                ResultBean<List<Map<String, String>>> mockResult = ResultBean.successfulResult(mockTypeList);
                when(anchorService.typeList()).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/type"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2))
                                .andExpect(jsonPath("$.resultData[0].usageScenario").value("广告配音"));

                verify(anchorService).typeList();
        }

        @Test
        @DisplayName("测试根据类型名称获取主播 - 成功场景")
        void testAnchorByTypeName_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                String name = "广告配音";
                Integer pageNumber = 0;
                Integer pageSize = 10;

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("total", 5L);
                mockResult.put("list", Arrays.asList(createMockAnchorVo(1L, "测试主播")));

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.anchorByTypeName(accessToken, name, pageNumber, pageSize))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(get("/home/<USER>/by/name")
                                .header("accessToken", accessToken)
                                .param("name", name)
                                .param("pageNumber", "0")
                                .param("pageSize", "10"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.total").value(5));

                verify(anchorService).anchorByTypeName(accessToken, name, pageNumber, pageSize);
        }

        @Test
        @DisplayName("测试长音频主播查询 - 成功场景")
        void testLongAchor_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                String name = "长音频场景";
                Integer pageNumber = 0;
                Integer pageSize = 10;

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("total", 3L);
                mockResult.put("list", Arrays.asList(createMockAnchorVo(1L, "智逍遥")));

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.longAchor(accessToken, name, pageNumber, pageSize))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(get("/home/<USER>/longAchor")
                                .header("accessToken", accessToken)
                                .param("name", name)
                                .param("pageNumber", "0")
                                .param("pageSize", "10"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.total").value(3));

                verify(anchorService).longAchor(accessToken, name, pageNumber, pageSize);
        }

        @Test
        @DisplayName("测试获取模板分类 - 成功场景")
        void testTemplateType_Success() throws Exception {
                // Given
                List<DubTemplateType> mockTemplateTypes = Arrays.asList(
                                createMockTemplateType(1L, "商业广告"),
                                createMockTemplateType(2L, "新闻播报"));
                ResultBean<List<DubTemplateType>> mockResult = ResultBean.successfulResult(mockTemplateTypes);
                when(anchorService.templateType()).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/templateType"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2));

                verify(anchorService).templateType();
        }

        private AnchorVo createMockAnchorVo(Long id, String name) {
                AnchorVo vo = new AnchorVo();
                vo.setId(id);
                vo.setName(name);
                vo.setUsageScenario("测试场景");
                vo.setTypeName("测试类型");
                vo.setUrl("http://test.com/avatar.jpg");
                vo.setVoiceUrl("http://test.com/voice.mp3");
                return vo;
        }

        private Map<String, String> createMockTypeMap(String usageScenario) {
                Map<String, String> map = new HashMap<>();
                map.put("usageScenario", usageScenario);
                return map;
        }

        private DubTemplateType createMockTemplateType(Long id, String name) {
                DubTemplateType templateType = new DubTemplateType();
                templateType.setId(id);
                templateType.setName(name);
                templateType.setDelStatus(0);
                return templateType;
        }

        @Test
        @DisplayName("测试试听功能 - 成功场景")
        void testAudition_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                SpeechSynthesizerDto dto = new SpeechSynthesizerDto();
                dto.setAnchorId(1L);
                dto.setText("测试文本");
                dto.setVolume(50);

                // Mock getUser方法
                AnchorController spyController = spy(anchorController);
                doReturn(mockTokenRedisVo).when(spyController).getUser(accessToken);

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "http://test.com/audio.mp3");
                mockResult.put("time", 10);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.audition(any(SpeechSynthesizerDto.class))).thenReturn(mockResultBean);

                // When & Then
                mockMvc = MockMvcBuilders.standaloneSetup(spyController).build();
                mockMvc.perform(post("/home/<USER>/audition")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(dto)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("http://test.com/audio.mp3"));

                verify(anchorService).audition(any(SpeechSynthesizerDto.class));
        }

        @Test
        @DisplayName("测试长音频合成 - 成功场景")
        void testLongCompound_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                SpeechSynthesizerDto dto = new SpeechSynthesizerDto();
                dto.setAnchorId(1L);
                dto.setText("长音频测试文本");

                AnchorController spyController = spy(anchorController);
                doReturn(mockTokenRedisVo).when(spyController).getUser(accessToken);

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "http://test.com/long-audio.mp3");
                mockResult.put("time", 60);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.longCompound(any(SpeechSynthesizerDto.class))).thenReturn(mockResultBean);

                // When & Then
                mockMvc = MockMvcBuilders.standaloneSetup(spyController).build();
                mockMvc.perform(post("/home/<USER>/longCompound")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(dto)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("http://test.com/long-audio.mp3"));

                verify(anchorService).longCompound(any(SpeechSynthesizerDto.class));
        }

        @Test
        @DisplayName("测试对话合成 - 成功场景")
        void testDialog_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                SpeechSynthesizerDto dto = new SpeechSynthesizerDto();
                dto.setAnchorId(1L);
                dto.setText("对话测试文本");

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "http://test.com/dialog-audio.mp3");
                mockResult.put("time", 15);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.dialog(any(SpeechSynthesizerDto.class), eq(accessToken)))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(post("/home/<USER>/dialog")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(dto)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("http://test.com/dialog-audio.mp3"));

                verify(anchorService).dialog(any(SpeechSynthesizerDto.class), eq(accessToken));
        }

        @Test
        @DisplayName("测试多人配音合并 - 成功场景")
        void testMergeAudio_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                MergeVo mergeVo = new MergeVo();
                mergeVo.setText("多人配音测试");
                mergeVo.setAudio(Arrays.asList("audio1.mp3", "audio2.mp3"));

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "http://test.com/merged-audio.mp3");
                mockResult.put("time", 30);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.mergeAudio(any(MergeVo.class), eq(accessToken)))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(post("/home/<USER>/mergeAudio")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mergeVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("http://test.com/merged-audio.mp3"));

                verify(anchorService).mergeAudio(any(MergeVo.class), eq(accessToken));
        }

        @Test
        @DisplayName("测试保存作品 - 成功场景")
        void testSave_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                VoiceWorkVo workVo = new VoiceWorkVo();
                workVo.setTitle("测试作品");
                workVo.setUrl("temp/test-audio.mp3");
                workVo.setAnchorId(1L);
                workVo.setText("测试文本");

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "voice/1/test-audio.mp3");
                mockResult.put("id", 1L);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.save(any(VoiceWorkVo.class), eq(accessToken)))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(post("/home/<USER>/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(workVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("voice/1/test-audio.mp3"));

                verify(anchorService).save(any(VoiceWorkVo.class), eq(accessToken));
        }

        @Test
        @DisplayName("测试保存作品 - 异常场景")
        void testSave_Exception() throws Exception {
                // Given
                String accessToken = "test-token";
                VoiceWorkVo workVo = new VoiceWorkVo();
                workVo.setTitle("测试作品");
                workVo.setUrl(""); // 空URL会导致异常

                when(anchorService.save(any(VoiceWorkVo.class), eq(accessToken)))
                                .thenThrow(new CustomException("音频文件不能为空"));

                // When & Then
                mockMvc.perform(post("/home/<USER>/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(workVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(false))
                                .andExpect(jsonPath("$.msg").value("音频文件不能为空")); // 注意：这里可能需要根据实际的异常处理机制调整

                verify(anchorService).save(any(VoiceWorkVo.class), eq(accessToken));
        }

        @Test
        @DisplayName("测试文件上传 - 成功场景")
        void testInput_Success() throws Exception {
                // Given
                MockMultipartFile file = new MockMultipartFile(
                                "file",
                                "test.mp3",
                                "audio/mpeg",
                                "test audio content".getBytes());
                String expectedUrl = "https://test-oss.com/temp/test.mp3";

                when(ossService.putFile(null, file, "temp")).thenReturn(expectedUrl);

                // When & Then
                mockMvc.perform(multipart("/home/<USER>/input")
                                .file(file))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value(expectedUrl));

                verify(ossService).putFile(null, file, "temp");
        }

        @Test
        @DisplayName("测试新增设备音频 - 成功场景")
        void testAddDeviceVoice_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                DeviceVoiceAddParam addParam = new DeviceVoiceAddParam();
                // 设置addParam的属性...

                // 配置RedisService mock行为
                when(redisService.findTokenVo(accessToken)).thenReturn(mockTokenRedisVo);

                ResultBean<Boolean> mockResult = ResultBean.successfulResult(true);
                when(deviceService.addDeviceVoice(any(DeviceVoiceAddParam.class)))
                                .thenReturn(mockResult);

                // When & Then
                mockMvc.perform(post("/home/<USER>/send")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(addParam)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").value(true));

                verify(deviceService).addDeviceVoice(any(DeviceVoiceAddParam.class));
        }

        @Test
        @DisplayName("测试上传录音 - 成功场景")
        void testUploadRecordFree_Success() throws Exception {
                // Given
                String accessToken = "test-token";
                MockMultipartFile file = new MockMultipartFile(
                                "file",
                                "record.wav",
                                "audio/wav",
                                "test record content".getBytes());
                RecordUploadReq uploadReq = new RecordUploadReq();
                uploadReq.setName("测试录音");
                uploadReq.setVolume(100);

                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("url", "http://test.com/processed-record.mp3");
                mockResult.put("time", 20);

                ResultBean<Map<String, Object>> mockResultBean = ResultBean.successfulResult(mockResult);
                when(anchorService.uploadRecordFree(any(MultipartFile.class), eq(accessToken),
                                any(RecordUploadReq.class)))
                                .thenReturn(mockResultBean);

                // When & Then
                mockMvc.perform(multipart("/home/<USER>/uploadRecordFree")
                                .file(file)
                                .header("accessToken", accessToken)
                                .param("name", "测试录音")
                                .param("volume", "100"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData.url").value("http://test.com/processed-record.mp3"));

                verify(anchorService).uploadRecordFree(any(MultipartFile.class), eq(accessToken),
                                any(RecordUploadReq.class));
        }

        @Test
        @DisplayName("测试获取AI分类列表 - 成功场景")
        void testAiClassList_Success() throws Exception {
                // Given
                List<AiClass> mockAiClassList = Arrays.asList(
                                createMockAiClass(1L, "商业广告"),
                                createMockAiClass(2L, "新闻播报"));
                ResultBean<List<AiClass>> mockResult = ResultBean.successfulResult(mockAiClassList);
                when(anchorService.aiClassList()).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/aiClassList"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2))
                                .andExpect(jsonPath("$.resultData[0].title").value("商业广告"));

                verify(anchorService).aiClassList();
        }

        @Test
        @DisplayName("测试获取AI风格列表 - 成功场景")
        void testAiStyleList_Success() throws Exception {
                // Given
                List<AiStyle> mockAiStyleList = Arrays.asList(
                                createMockAiStyle(1L, "正式"),
                                createMockAiStyle(2L, "轻松"));
                ResultBean<List<AiStyle>> mockResult = ResultBean.successfulResult(mockAiStyleList);
                when(anchorService.aiStyleList()).thenReturn(mockResult);

                // When & Then
                mockMvc.perform(get("/home/<USER>/aiStyleList"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.ok").value(true))
                                .andExpect(jsonPath("$.resultData").isArray())
                                .andExpect(jsonPath("$.resultData.length()").value(2))
                                .andExpect(jsonPath("$.resultData[0].title").value("正式"));

                verify(anchorService).aiStyleList();
        }

        private AiClass createMockAiClass(Long id, String title) {
                AiClass aiClass = new AiClass();
                aiClass.setId(id);
                aiClass.setTitle(title);
                aiClass.setContent(title + "内容");
                aiClass.setDelStatus(0);
                aiClass.setOrderIndex(1);
                return aiClass;
        }

        private AiStyle createMockAiStyle(Long id, String title) {
                AiStyle aiStyle = new AiStyle();
                aiStyle.setId(id);
                aiStyle.setTitle(title);
                aiStyle.setContent(title + "风格");
                aiStyle.setDelStatus(0);
                aiStyle.setOrderIndex(1);
                return aiStyle;
        }
}
