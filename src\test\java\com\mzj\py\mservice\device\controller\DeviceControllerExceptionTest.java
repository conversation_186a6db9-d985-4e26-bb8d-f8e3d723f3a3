package com.mzj.py.mservice.device.controller;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.Device;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.util.NestedServletException;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * DeviceController异常处理测试类
 * 专门测试各种异常情况和边界条件
 */
@ExtendWith(MockitoExtension.class)
class DeviceControllerExceptionTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;

    private MockMvc mockMvc;
    private static final String ACCESS_TOKEN = "test-access-token";
    private static final String ACCESS_TOKEN_HEADER = "accessToken";

    @BeforeEach
    void setUp() {
        deviceController = spy(new DeviceController());
        try {
            java.lang.reflect.Field field = DeviceController.class.getDeclaredField("deviceService");
            field.setAccessible(true);
            field.set(deviceController, deviceService);
        } catch (Exception e) {
            // fallback
        }

        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();

        // Mock ApiBaseController methods - use lenient to avoid unnecessary stubbing
        // exceptions
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        lenient().doReturn(Arrays.asList(1L, 2L)).when(deviceController).getShopIds(ACCESS_TOKEN);
        lenient().doReturn(mockUser).when(deviceController).getUser(ACCESS_TOKEN);
        lenient().doReturn(1L).when(deviceController).getShopId(ACCESS_TOKEN);
    }

    @Test
    void 测试设备列表查询_服务层抛出异常() throws Exception {
        // Given
        DeviceQueryVo queryVo = new DeviceQueryVo();
        queryVo.setPageSize(10);
        queryVo.setPageNumber(1);

        when(deviceService.list(any(DeviceQueryVo.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/list")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(queryVo)));
        });
    }

    @Test
    void 测试设备解绑_服务层抛出业务异常() throws Exception {
        // Given
        when(deviceService.unBind(1L))
                .thenThrow(new ServiceException("设备操作失败"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/unBind/1")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    void 测试设备绑定门店_服务层抛出运行时异常() throws Exception {
        // Given
        DeviceUnBindVo bindVo = new DeviceUnBindVo();
        bindVo.setId(1L);
        bindVo.setShopId(1L);

        when(deviceService.bind(any(DeviceUnBindVo.class)))
                .thenThrow(new RuntimeException("权限不足"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/bindShop")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(bindVo)));
        });
    }

    @Test
    void 测试新增设备_服务层抛出事务异常() throws Exception {
        // Given
        DeviceAddVo addVo = new DeviceAddVo();
        addVo.setName("Test Device");
        addVo.setSn("TEST001");

        lenient().when(deviceService.addOrUpdate(any(Device.class)))
                .thenThrow(new RuntimeException("Transaction rollback"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/add")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(addVo)));
        });
    }

    @Test
    void 测试更新设备_服务层抛出空指针异常() throws Exception {
        // Given
        DeviceUpdateVo updateVo = new DeviceUpdateVo();
        updateVo.setId(1L);
        updateVo.setName("Updated Device");

        lenient().when(deviceService.addOrUpdate(any(Device.class)))
                .thenThrow(new NullPointerException("Device not found"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/update")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(updateVo)));
        });
    }

    @Test
    void 测试设备详情查询_服务层抛出非法参数异常() throws Exception {
        // Given
        when(deviceService.detail(1L, 10, 0))
                .thenThrow(new IllegalArgumentException("Invalid device ID"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/detail")
                    .param("deviceId", "1")
                    .param("pageSize", "10")
                    .param("pageNumber", "0"));
        });
    }

    @Test
    void 测试设备详情查询包含语音列表_服务层抛出异常() throws Exception {
        // Given
        List<DevVoiceVo> voiceList = Arrays.asList(new DevVoiceVo());

        when(deviceService.deviceVoicedetail(eq(1L), anyList()))
                .thenThrow(new RuntimeException("Voice processing failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/device/detail/1")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(voiceList)));
        });
    }

    @Test
    void 测试更新设备语音_端点不存在应返回404() throws Exception {
        // Given - updateDeviceVoice端点在DeviceController中被注释掉了
        DeviceVoiceVo voiceVo = new DeviceVoiceVo();
        voiceVo.setId(1L);
        voiceVo.setDeviceId(1L);

        // When & Then - 由于端点不存在，应该返回404而不是抛出NestedServletException
        mockMvc.perform(post("/mini/device/devicevoice")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(voiceVo)))
                .andExpect(status().isNotFound());
    }

    @Test
    void 测试删除设备语音_服务层抛出异常() throws Exception {
        // Given
        when(deviceService.deleteDeviceVoice(1L, 1L))
                .thenThrow(new RuntimeException("Delete operation failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(delete("/mini/device")
                    .param("id", "1")
                    .param("deviceId", "1"));
        });
    }

    @Test
    void 测试获取用户设备树形选择列表_服务层抛出异常() throws Exception {
        // Given
        lenient().when(deviceService.getTreeUserDeviceSelect(anyList()))
                .thenThrow(new RuntimeException("Tree query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/device/tree/my")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    void 测试无效请求体_格式错误的JSON() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试无效请求体_空JSON() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(""))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试无效请求体_空请求体() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试无效路径变量_非数字类型() throws Exception {
        // When & Then
        mockMvc.perform(get("/mini/device/unBind/abc")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试无效路径变量_负数() throws Exception {
        // Given
        when(deviceService.unBind(-1L))
                .thenReturn(ResultBean.failedResultWithMsg("无效的设备ID"));

        // When & Then
        mockMvc.perform(get("/mini/device/unBind/-1")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("无效的设备ID"));
    }

    @Test
    void 测试无效请求参数_非数字类型() throws Exception {
        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                .param("deviceId", "abc")
                .param("pageSize", "10")
                .param("pageNumber", "0"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试无效请求参数_负数页面大小() throws Exception {
        // Given
        when(deviceService.detail(1L, -10, 0))
                .thenReturn(ResultBean.failedResultWithMsg("页面大小不能为负数"));

        // When & Then
        mockMvc.perform(get("/mini/device/detail")
                .param("deviceId", "1")
                .param("pageSize", "-10")
                .param("pageNumber", "0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false))
                .andExpect(jsonPath("$.msg").value("页面大小不能为负数"));
    }

    @Test
    void 测试缺少必需参数() throws Exception {
        // When & Then
        mockMvc.perform(delete("/mini/device"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void 测试不支持的媒体类型() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text"))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    void 测试不支持的HTTP方法() throws Exception {
        // When & Then
        mockMvc.perform(put("/mini/device/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isMethodNotAllowed());
    }
}
