package com.mzj.py.mservice.device.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.BindStatusEnum;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.service.StoreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeviceService性能测试类
 * 测试大数据量和高并发场景下的性能表现
 */
@ExtendWith(MockitoExtension.class)
class DeviceServicePerformanceTest {

        @InjectMocks
        private DeviceService deviceService;

        @Mock
        private JdbcTemplate jdbcTemplate;
        @Mock
        private DeviceRepository deviceRepository;
        @Mock
        private DeviceVoiceRepository deviceVoiceRepository;
        @Mock
        private OSSService ossService;
        @Mock
        private StoreService shopService;
        @Mock
        private ShopRepository shopRepository;
        @Mock
        private ShopUserRefRepository shopUserRefRepository;
        @Mock
        private RemoteDeviceService remoteDeviceService;
        @Mock
        private VoicePacketRepository voicePacketRepository;
        @Mock
        private VoiceWorkRepository workRepository;

        @BeforeEach
        void setUp() {
                ReflectionTestUtils.setField(deviceService, "cdnUrl", "https://test-oss.com/");
        }

        @Test
        @DisplayName("测试大量设备列表查询性能 - 十万级数据量")
        void testLargeDeviceListQuery() {
                // 测试大量设备列表查询性能 - 模拟十万级数据量
                DeviceQueryVo queryVo = new DeviceQueryVo();
                queryVo.setShopIds(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L));
                queryVo.setCreateId(1L);
                queryVo.setPageSize(500);
                queryVo.setPageNumber(1);

                // 模拟100000个设备 - 大数据量性能测试
                List<Device> devices = IntStream.range(0, 100000)
                                .mapToObj(i -> createTestDevice((long) i))
                                .collect(java.util.stream.Collectors.toList());

                Page<Device> page = new PageImpl<>(devices.subList(0, 500),
                                mock(Pageable.class), 100000);

                when(deviceRepository.findAll(any(Specification.class), any(Pageable.class)))
                                .thenReturn(page);

                long startTime = System.currentTimeMillis();
                ResultBean<Map<String, Object>> result = deviceService.list(queryVo);
                long endTime = System.currentTimeMillis();

                assertTrue(result.isOk());
                Map<String, Object> data = result.getResultData();
                assertEquals(100000, data.get("count"));
                assertEquals(500, ((List<?>) data.get("result")).size());

                // 性能断言：十万级数据查询应该在500ms内完成
                assertTrue(endTime - startTime < 500,
                                "Large device list query with 100k records took too long: " + (endTime - startTime)
                                                + "ms");
        }

        @Test
        @DisplayName("测试批量设备语音创建性能 - 百个设备音频批量处理")
        void testBatchDeviceVoiceCreation() {
                // 测试批量设备语音创建性能
                DeviceVoiceAddParam addParam = new DeviceVoiceAddParam();

                // 模拟100个设备
                List<Long> deviceIds = IntStream.range(1, 101)
                                .mapToObj(Long::valueOf)
                                .collect(java.util.stream.Collectors.toList());
                addParam.setDeviceIds(deviceIds);
                addParam.setTitle("Batch Voice Test");
                addParam.setContent("Test content");
                addParam.setVoiceId(1L);
                addParam.setBackgroundMusicId(1L);
                addParam.setBackgroundMusicVolume(30);
                addParam.setShopId(1L);
                addParam.setUserId(1L);
                addParam.setUrl("test-voice-url.mp3");
                addParam.setTime(10);
                addParam.setSpeed(50);
                addParam.setVolume(50);
                addParam.setPitch(50);
                addParam.setType(1);

                VoicePacket voicePacket = new VoicePacket();
                voicePacket.setId(1L);
                VoiceWork voiceWork = new VoiceWork();
                voiceWork.setId(1L);

                // Mock OSS服务调用
                File mockFile = new File("test.mp3");
                when(ossService.getObjectFile(null, "test-voice-url.mp3")).thenReturn(mockFile);
                when(ossService.putFileToName(eq(null), eq(mockFile), eq("voice"), anyString()))
                                .thenReturn("new-voice-url.mp3");
                when(ossService.putFileToName(eq(null), eq(mockFile), eq("device"), anyString()))
                                .thenReturn("device-voice-url.mp3");

                when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(voicePacket);
                when(workRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);
                when(deviceVoiceRepository.saveAll(anyList())).thenReturn(Arrays.asList(new DeviceVoice()));
                when(deviceVoiceRepository.findByDeviceIdAndTitleAndDelStatus(anyLong(), anyString(), eq(0)))
                                .thenReturn(new ArrayList<>());

                long startTime = System.currentTimeMillis();
                ResultBean<Boolean> result = deviceService.addDeviceVoice(addParam);
                long endTime = System.currentTimeMillis();

                assertTrue(result.isOk());
                verify(deviceVoiceRepository).saveAll(argThat(list -> ((List<?>) list).size() == 100));
                verify(remoteDeviceService, times(100)).sendAudio(anyLong(), anyString(), anyLong(), anyString());

                // 性能断言：批量创建应该在500ms内完成
                assertTrue(endTime - startTime < 500,
                                "Batch device voice creation took too long: " + (endTime - startTime) + "ms");
        }

        @Test
        @DisplayName("测试并发设备操作性能 - 千级并发压力测试")
        void testConcurrentDeviceOperations() throws InterruptedException {
                // 测试并发设备操作性能 - 千级并发压力测试
                Device device = createTestDevice(1L);
                when(deviceRepository.findById(1L)).thenReturn(Optional.of(device));
                when(deviceRepository.save(any(Device.class))).thenReturn(device);

                ExecutorService executor = Executors.newFixedThreadPool(50);
                List<CompletableFuture<ResultBean<Boolean>>> futures = new ArrayList<>();

                long startTime = System.currentTimeMillis();

                // 启动1000个并发解绑操作 - 高并发压力测试
                for (int i = 0; i < 1000; i++) {
                        CompletableFuture<ResultBean<Boolean>> future = CompletableFuture.supplyAsync(
                                        () -> deviceService.unBind(1L), executor);
                        futures.add(future);
                }

                // 等待所有操作完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                long endTime = System.currentTimeMillis();

                // 验证所有操作都成功
                for (CompletableFuture<ResultBean<Boolean>> future : futures) {
                        try {
                                assertTrue(future.get().isOk());
                        } catch (Exception e) {
                                fail("Future execution failed: " + e.getMessage());
                        }
                }

                executor.shutdown();
                assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));

                // 性能断言：1000个并发操作应该在5秒内完成
                assertTrue(endTime - startTime < 5000,
                                "Concurrent device operations with 1000 threads took too long: " + (endTime - startTime)
                                                + "ms");

                verify(deviceRepository, times(1000)).findById(1L);
                verify(deviceRepository, times(1000)).save(any(Device.class));
        }

        @Test
        @DisplayName("测试大量设备语音详情查询性能 - 百万级语音记录")
        void testLargeDeviceVoiceDetailQuery() {
                // 测试大量设备语音详情查询性能 - 百万级语音记录
                Long deviceId = 1L;
                Device device = createTestDevice(deviceId);

                when(deviceRepository.findById(deviceId)).thenReturn(java.util.Optional.of(device));

                // 模拟1000000条语音记录 - 百万级数据量测试
                when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                                .thenReturn(1000000L);

                List<DeviceVoice> deviceVoices = IntStream.range(0, 200) // 分页返回200条
                                .mapToObj(i -> createTestDeviceVoice((long) i))
                                .collect(java.util.stream.Collectors.toList());

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(), any(), any()))
                                .thenReturn(deviceVoices);

                long startTime = System.currentTimeMillis();
                ResultBean<DetailVo> result = deviceService.detail(deviceId, 200, 0);
                long endTime = System.currentTimeMillis();

                assertTrue(result.isOk());
                DetailVo detailVo = result.getResultData();
                assertNotNull(detailVo);
                Map<String, Object> map = detailVo.getMap();
                assertEquals(1000000L, map.get("count"));
                assertEquals(200, ((List<?>) map.get("result")).size());

                // 性能断言：百万级数据详情查询应该在800ms内完成
                assertTrue(endTime - startTime < 800,
                                "Large device voice detail query with 1M records took too long: "
                                                + (endTime - startTime) + "ms");
        }

        @Test
        @DisplayName("测试大量操作时的内存使用情况 - 内存泄漏检测")
        void testMemoryUsageInLargeOperations() {
                // 测试大量操作时的内存使用情况
                Runtime runtime = Runtime.getRuntime();
                long initialMemory = runtime.totalMemory() - runtime.freeMemory();

                // 执行大量设备查询操作
                DeviceQueryVo queryVo = new DeviceQueryVo();
                queryVo.setShopIds(Arrays.asList(1L, 2L, 3L, 4L, 5L));
                queryVo.setCreateId(1L);
                queryVo.setPageSize(1000);
                queryVo.setPageNumber(1);

                List<Device> devices = IntStream.range(0, 1000)
                                .mapToObj(i -> createTestDevice((long) i))
                                .collect(java.util.stream.Collectors.toList());

                Page<Device> page = new PageImpl<>(devices, mock(Pageable.class), 1000);
                when(deviceRepository.findAll(any(Specification.class), any(Pageable.class)))
                                .thenReturn(page);

                // 执行多次查询
                for (int i = 0; i < 10; i++) {
                        ResultBean<Map<String, Object>> result = deviceService.list(queryVo);
                        assertTrue(result.isOk());
                }

                // 强制垃圾回收
                System.gc();
                try {
                        Thread.sleep(100);
                } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                }

                long finalMemory = runtime.totalMemory() - runtime.freeMemory();
                long memoryIncrease = finalMemory - initialMemory;

                // 内存增长不应该超过50MB
                assertTrue(memoryIncrease < 50 * 1024 * 1024,
                                "Memory usage increased too much: " + (memoryIncrease / 1024 / 1024) + "MB");
        }

        @Test
        @DisplayName("测试数据库连接池压力 - 小规模并发查询")
        void testDatabaseConnectionPoolStress() {
                // 测试数据库连接池压力 - 小规模并发查询（减少到100个并发）
                DeviceQueryVo queryVo = new DeviceQueryVo();
                queryVo.setShopIds(Arrays.asList(1L, 2L, 3L, 4L, 5L));
                queryVo.setCreateId(1L);
                queryVo.setPageSize(50);
                queryVo.setPageNumber(1);

                List<Device> devices = Arrays.asList(createTestDevice(1L));
                Page<Device> page = new PageImpl<>(devices, mock(Pageable.class), 1);
                when(deviceRepository.findAll(any(Specification.class), any(Pageable.class)))
                                .thenReturn(page);

                ExecutorService executor = Executors.newFixedThreadPool(10);
                List<CompletableFuture<ResultBean<Map<String, Object>>>> futures = new ArrayList<>();

                long startTime = System.currentTimeMillis();

                // 启动100个并发查询操作 - 小规模并发压力测试
                for (int i = 0; i < 100; i++) {
                        CompletableFuture<ResultBean<Map<String, Object>>> future = CompletableFuture.supplyAsync(
                                        () -> deviceService.list(queryVo), executor);
                        futures.add(future);
                }

                // 等待所有操作完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                long endTime = System.currentTimeMillis();

                // 验证所有操作都成功
                for (CompletableFuture<ResultBean<Map<String, Object>>> future : futures) {
                        try {
                                assertTrue(future.get().isOk());
                        } catch (Exception e) {
                                fail("Future execution failed: " + e.getMessage());
                        }
                }

                executor.shutdown();
                try {
                        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
                } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                }

                // 性能断言：100个并发查询应该在10秒内完成
                assertTrue(endTime - startTime < 10000,
                                "Database connection pool stress test with 100 queries took too long: "
                                                + (endTime - startTime) + "ms");

                verify(deviceRepository, times(100)).findAll(any(Specification.class), any(Pageable.class));
        }

        @Test
        @DisplayName("测试设备音频批量创建性能 - 中等规模数据测试")
        void testMassiveDeviceVoiceBatchCreation() {
                // Given - 准备较小规模的测试数据（减少到10个设备以避免性能问题）
                DeviceVoiceAddParam addParam = new DeviceVoiceAddParam();
                List<Long> deviceIds = IntStream.range(1, 11) // 10个设备
                                .mapToObj(Long::valueOf)
                                .collect(java.util.stream.Collectors.toList());
                addParam.setDeviceIds(deviceIds);
                addParam.setTitle("设备音频性能测试");
                addParam.setContent("这是设备音频数据的性能测试内容");
                addParam.setVoiceId(1L);
                addParam.setBackgroundMusicId(1L);
                addParam.setBackgroundMusicVolume(30);
                addParam.setShopId(1L);
                addParam.setUserId(1L);
                addParam.setUrl("test-voice.mp3");
                addParam.setTime(60);
                addParam.setSpeed(50);
                addParam.setVolume(50);
                addParam.setPitch(50);
                addParam.setType(1);

                // Mock 数据操作
                VoicePacket mockVoicePacket = new VoicePacket();
                mockVoicePacket.setId(1L);
                VoiceWork mockVoiceWork = new VoiceWork();
                mockVoiceWork.setId(1L);
                DeviceVoice mockDeviceVoice = new DeviceVoice();
                mockDeviceVoice.setId(1L);

                // Mock OSS服务调用
                File mockFile = new File("test-voice.mp3");
                when(ossService.getObjectFile(null, "test-voice.mp3")).thenReturn(mockFile);
                when(ossService.putFileToName(eq(null), eq(mockFile), eq("voice"), anyString()))
                                .thenReturn("new-voice-url.mp3");
                when(ossService.putFileToName(eq(null), eq(mockFile), eq("device"), anyString()))
                                .thenReturn("device-voice-url.mp3");

                when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(mockVoicePacket);
                when(workRepository.save(any(VoiceWork.class))).thenReturn(mockVoiceWork);
                when(deviceVoiceRepository.saveAll(anyList())).thenReturn(java.util.Arrays.asList(mockDeviceVoice));
                when(deviceVoiceRepository.findByDeviceIdAndTitleAndDelStatus(anyLong(), anyString(), eq(0)))
                                .thenReturn(new ArrayList<>());

                // When - 执行批量创建并测量时间
                long startTime = System.currentTimeMillis();

                // 模拟分批处理
                for (int batch = 0; batch < 5; batch++) { // 5批次，每批10个设备
                        ResultBean<Boolean> result = deviceService.addDeviceVoice(addParam);
                        assertTrue(result.isOk());
                }

                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                // Then - 验证性能
                System.out.println("设备音频批量创建耗时: " + executionTime + "ms");
                assertTrue(executionTime < 30000, "设备音频创建应在30秒内完成");

                verify(deviceVoiceRepository, times(5)).saveAll(anyList());
                verify(remoteDeviceService, times(50)).sendAudio(anyLong(), anyString(), anyLong(), anyString());
        }

        @Test
        @DisplayName("测试设备音频海量数据复杂查询性能 - 多条件组合查询")
        void testComplexDeviceVoiceQuery() {
                // Given - 准备复杂查询条件和海量数据
                Long deviceId = 1L;
                Device device = createTestDevice(deviceId);

                when(deviceRepository.findById(deviceId)).thenReturn(java.util.Optional.of(device));

                // 模拟500万条设备音频记录
                when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                                .thenReturn(5000000L);

                // 模拟复杂查询结果 - 多表关联查询
                List<DeviceVoice> complexQueryResults = IntStream.range(0, 500)
                                .mapToObj(i -> {
                                        DeviceVoice voice = createTestDeviceVoice((long) i);
                                        voice.setTitle("复杂查询测试音频_" + i);
                                        voice.setContent("包含关键词的复杂查询内容_" + i);
                                        return voice;
                                })
                                .collect(java.util.stream.Collectors.toList());

                when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(), any(), any()))
                                .thenReturn(complexQueryResults);

                // When - 执行复杂查询并测量时间
                long startTime = System.currentTimeMillis();

                // 模拟多种复杂查询场景
                for (int i = 0; i < 1000; i++) {
                        ResultBean<DetailVo> result = deviceService.detail(deviceId, 500, 0);
                        assertTrue(result.isOk());
                }

                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                // Then - 验证复杂查询性能
                System.out.println("设备音频海量数据复杂查询1000次耗时: " + executionTime + "ms");
                assertTrue(executionTime < 60000, "海量数据复杂查询应在60秒内完成");

                verify(jdbcTemplate, times(1000)).queryForObject(anyString(), eq(Long.class), any());
                verify(jdbcTemplate, times(1000)).query(anyString(), any(BeanPropertyRowMapper.class), any(), any(),
                                any());
        }

        @Test
        @DisplayName("测试设备音频数据批量删除性能 - 大规模数据清理")
        void testMassiveDeviceVoiceBatchDeletion() {
                // Given - 准备较小规模的测试数据（减少到1000条以避免性能问题）
                int deletionSize = 1000;
                List<Long> voiceIds = IntStream.range(1, deletionSize + 1)
                                .mapToObj(Long::valueOf)
                                .collect(java.util.stream.Collectors.toList());

                // 创建测试用的DeviceVoice对象
                DeviceVoice testDeviceVoice = new DeviceVoice();
                testDeviceVoice.setId(1L);
                testDeviceVoice.setDeviceId(1L);
                testDeviceVoice.setTitle("Test Voice");

                // Mock 查找和删除操作 - 关键修复：使用正确的findByIdAndDeviceId方法
                when(deviceVoiceRepository.findByIdAndDeviceId(anyLong(), anyLong())).thenReturn(testDeviceVoice);
                when(deviceVoiceRepository.updateDelStatusById(anyLong())).thenReturn(1);

                // When - 执行大规模批量删除并测量时间
                long startTime = System.currentTimeMillis();

                // 分批删除以模拟实际场景
                int batchSize = 100;
                for (int i = 0; i < voiceIds.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, voiceIds.size());
                        List<Long> batch = voiceIds.subList(i, endIndex);

                        for (Long voiceId : batch) {
                                deviceService.deleteDeviceVoice(1L, voiceId);
                        }
                }

                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                // Then - 验证批量删除性能
                System.out.println("批量删除" + deletionSize + "条设备音频数据耗时: " + executionTime + "ms");
                assertTrue(executionTime < 10000, "批量删除1000条数据应在10秒内完成");

                verify(deviceVoiceRepository, times(deletionSize)).findByIdAndDeviceId(anyLong(), anyLong());
                verify(deviceVoiceRepository, times(deletionSize)).updateDelStatusById(anyLong());
        }

        @Test
        @DisplayName("测试设备音频数据统计分析性能 - 大数据聚合查询")
        void testDeviceVoiceStatisticsPerformance() {
                // Given - 准备统计分析查询
                Long shopId = 1L;

                // Mock 统计查询结果 - 模拟复杂的聚合查询
                when(jdbcTemplate.queryForObject(contains("COUNT"), eq(Long.class), any()))
                                .thenReturn(2000000L); // 200万条记录

                when(jdbcTemplate.queryForObject(contains("AVG"), eq(Double.class), any()))
                                .thenReturn(45.5); // 平均时长

                when(jdbcTemplate.queryForObject(contains("MAX"), eq(Integer.class), any()))
                                .thenReturn(300); // 最大时长

                when(jdbcTemplate.queryForObject(contains("MIN"), eq(Integer.class), any()))
                                .thenReturn(10); // 最小时长

                // When - 执行统计分析查询并测量时间
                long startTime = System.currentTimeMillis();

                // 模拟多种统计查询
                for (int i = 0; i < 100; i++) {
                        // 总数统计
                        Long totalCount = jdbcTemplate.queryForObject(
                                        "SELECT COUNT(*) FROM dub_device_voice WHERE shop_id = ? AND del_status = 0",
                                        Long.class, shopId);

                        // 平均时长统计
                        Double avgDuration = jdbcTemplate.queryForObject(
                                        "SELECT AVG(voice_time) FROM dub_device_voice WHERE shop_id = ? AND del_status = 0",
                                        Double.class, shopId);

                        // 最大最小时长统计
                        Integer maxDuration = jdbcTemplate.queryForObject(
                                        "SELECT MAX(voice_time) FROM dub_device_voice WHERE shop_id = ? AND del_status = 0",
                                        Integer.class, shopId);

                        Integer minDuration = jdbcTemplate.queryForObject(
                                        "SELECT MIN(voice_time) FROM dub_device_voice WHERE shop_id = ? AND del_status = 0",
                                        Integer.class, shopId);

                        // 验证统计结果
                        assertNotNull(totalCount);
                        assertNotNull(avgDuration);
                        assertNotNull(maxDuration);
                        assertNotNull(minDuration);
                }

                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                // Then - 验证统计查询性能
                System.out.println("设备音频大数据统计分析100次查询耗时: " + executionTime + "ms");
                assertTrue(executionTime < 30000, "大数据统计分析应在30秒内完成");

                verify(jdbcTemplate, times(400)).queryForObject(anyString(), any(Class.class), any());
        }

        // Helper methods
        private Device createTestDevice(Long id) {
                Device device = new Device();
                device.setId(id);
                device.setName("性能测试设备_" + id);
                device.setSn("PERF_TEST_" + String.format("%06d", id));
                device.setUserId((id % 10000) + 1); // 分布在1万个用户中
                device.setShopId((id % 1000) + 1); // 分布在1000个店铺中
                device.setVolume((int) (id % 100 + 1));
                device.setStatus(1);
                device.setBindStatus(BindStatusEnum.BINDING.ordinal());
                device.setDelStatus(0);
                device.setCreateTime(new Date());
                return device;
        }

        private DeviceVoice createTestDeviceVoice(Long id) {
                DeviceVoice voice = new DeviceVoice();
                voice.setId(id);
                voice.setDeviceId((id % 10000) + 1); // 分布在1万个设备中
                voice.setVoiceWorkId((id % 100000) + 1); // 关联到10万个作品
                voice.setTitle("性能测试设备音频_" + id);
                voice.setContent("这是第" + id + "个性能测试设备音频的内容，用于测试大数据量场景。");
                voice.setVoiceUrl("https://perf-test-oss.com/device-voice/test_" + id + ".mp3");
                voice.setSpeed((int) (id % 100 + 1));
                voice.setVolume((int) (id % 100 + 1));
                voice.setPitch((int) (id % 100 + 1));
                voice.setBackgroundMusicVolume((int) (id % 100 + 1));
                voice.setVoiceTime((int) (id % 300 + 30)); // 30-330秒
                voice.setType(1);
                voice.setDelStatus(0);
                return voice;
        }
}
